using System.Windows;

namespace TraceLens.MainApp.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void ShowIconDemo_Click(object sender, RoutedEventArgs e)
        {
            var iconDemoWindow = new IconDemoWindow();
            iconDemoWindow.Show();
        }

        private void ShowFontTest_Click(object sender, RoutedEventArgs e)
        {
            var fontTestWindow = new FontTestWindow();
            fontTestWindow.Show();
        }
    }
}
