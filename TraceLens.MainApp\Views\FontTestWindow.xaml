<Window x:Class="TraceLens.MainApp.Views.FontTestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:TraceLens.ThemeControl.Controls;assembly=TraceLens.ThemeControl"
        Title="字体测试" Height="600" Width="800">
    <Grid>
        <ScrollViewer>
            <StackPanel Margin="20">
                <TextBlock Text="字体测试窗口" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
                
                <!-- 直接使用Unicode字符测试 -->
                <GroupBox Header="直接Unicode字符测试" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock Text="使用默认字体 (Segoe UI):" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="使用Segoe UI Symbol:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" FontFamily="Segoe UI Symbol" FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="尝试FontAwesome路径1:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/#FontAwesome" 
                                   FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="尝试FontAwesome路径2:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome" 
                                   FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="尝试FontAwesome路径3:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf#FontAwesome" 
                                   FontSize="24" Margin="0,5,0,10"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- HIcon控件测试 -->
                <GroupBox Header="HIcon控件测试" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock Text="使用IconName:" FontWeight="Bold"/>
                        <StackPanel Orientation="Horizontal" Margin="0,5,0,10">
                            <controls:HIcon IconName="home" IconSize="24" Margin="5"/>
                            <controls:HIcon IconName="user" IconSize="24" Margin="5"/>
                            <controls:HIcon IconName="settings" IconSize="24" Margin="5"/>
                            <controls:HIcon IconName="camera" IconSize="24" Margin="5"/>
                        </StackPanel>
                        
                        <TextBlock Text="使用IconCode:" FontWeight="Bold"/>
                        <StackPanel Orientation="Horizontal" Margin="0,5,0,10">
                            <controls:HIcon IconCode="&#xf015;" IconSize="24" Margin="5"/>
                            <controls:HIcon IconCode="&#xf007;" IconSize="24" Margin="5"/>
                            <controls:HIcon IconCode="&#xf013;" IconSize="24" Margin="5"/>
                            <controls:HIcon IconCode="&#xf030;" IconSize="24" Margin="5"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 字体信息 -->
                <GroupBox Header="字体信息" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock x:Name="FontInfoText" Text="字体信息将在窗口加载后显示..." FontFamily="Consolas"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
