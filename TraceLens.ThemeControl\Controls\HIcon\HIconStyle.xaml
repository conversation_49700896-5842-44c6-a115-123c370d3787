<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:TraceLens.ThemeControl.Controls">
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#FF222222"/>
    <!-- HIcon 控件默认样式 -->
    <Style TargetType="{x:Type controls:HIcon}">
        <Setter Property="FontSize" Value="{StaticResource IconSizeMedium}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="TextAlignment" Value="Center"/>
    </Style>

    <!-- 小图标样式 -->
    <Style x:Key="SmallIcon" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="FontSize" Value="{StaticResource IconSizeSmall}"/>
    </Style>

    <!-- 大图标样式 -->
    <Style x:Key="LargeIcon" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="FontSize" Value="{StaticResource IconSizeLarge}"/>
    </Style>

    <!-- 超大图标样式 -->
    <Style x:Key="XLargeIcon" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="FontSize" Value="{StaticResource IconSizeXLarge}"/>
    </Style>

    <!-- 强调色图标样式 -->
    <Style x:Key="AccentIcon" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource AccentBrush}"/>
    </Style>

    <!-- 成功状态图标样式 -->
    <Style x:Key="SuccessIcon" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource SuccessBrush}"/>
    </Style>

    <!-- 警告状态图标样式 -->
    <Style x:Key="WarningIcon" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource WarningBrush}"/>
    </Style>

    <!-- 错误状态图标样式 -->
    <Style x:Key="ErrorIcon" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource ErrorBrush}"/>
    </Style>

    <!-- 信息状态图标样式 -->
    <Style x:Key="InfoIcon" TargetType="{x:Type controls:HIcon}" BasedOn="{StaticResource {x:Type controls:HIcon}}">
        <Setter Property="IconColor" Value="{StaticResource InfoBrush}"/>
    </Style>

</ResourceDictionary>
