<Window x:Class="TraceLens.MainApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:prism="http://prismlibrary.com/"
        xmlns:controls="clr-namespace:TraceLens.ThemeControl.Controls;assembly=TraceLens.ThemeControl"
        prism:ViewModelLocator.AutoWireViewModel="True"
        mc:Ignorable="d"
        Title="{Binding Title}" Height="600" Width="1000"
        Style="{StaticResource IndustrialWindow}">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo/Icon -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <controls:HIcon IconName="industry" IconSize="24" IconColor="{StaticResource AccentBrush}" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding Title}"
                               Style="{StaticResource TitleText}"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Header Buttons -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Style="{StaticResource IndustrialButton}" Margin="5,0" Padding="8">
                        <StackPanel Orientation="Horizontal">
                            <controls:HIcon IconName="settings" IconSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="设置"/>
                        </StackPanel>
                    </Button>
                    <Button Style="{StaticResource IndustrialButton}" Margin="5,0" Padding="8">
                        <StackPanel Orientation="Horizontal">
                            <controls:HIcon IconName="user" IconSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="用户"/>
                        </StackPanel>
                    </Button>
                    <Button Style="{StaticResource IndustrialButton}" Margin="5,0" Padding="8" Click="ShowIconDemo_Click">
                        <StackPanel Orientation="Horizontal">
                            <controls:HIcon IconName="eye" IconSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="图标演示"/>
                        </StackPanel>
                    </Button>
                    <Button Style="{StaticResource IndustrialButton}" Margin="5,0" Padding="8" Click="ShowFontTest_Click">
                        <StackPanel Orientation="Horizontal">
                            <controls:HIcon IconName="wrench" IconSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="字体测试"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content Area -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Navigation -->
            <Border Grid.Column="0" Background="{StaticResource SurfaceBrush}"
                    BorderBrush="{StaticResource BorderBrush}" BorderThickness="1"
                    CornerRadius="4" Margin="0,0,10,0">
                <StackPanel Margin="15">
                    <TextBlock Text="功能导航" Style="{StaticResource SubtitleText}" Margin="0,0,0,15"/>

                    <!-- Navigation Buttons -->
                    <Button Style="{StaticResource IndustrialButton}" Margin="0,5" HorizontalAlignment="Stretch">
                        <StackPanel Orientation="Horizontal">
                            <controls:HIcon IconName="camera" IconSize="18" Margin="0,0,10,0"/>
                            <TextBlock Text="视觉检测"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource IndustrialButton}" Margin="0,5" HorizontalAlignment="Stretch">
                        <StackPanel Orientation="Horizontal">
                            <controls:HIcon IconName="chart" IconSize="18" Margin="0,0,10,0"/>
                            <TextBlock Text="数据分析"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource IndustrialButton}" Margin="0,5" HorizontalAlignment="Stretch">
                        <StackPanel Orientation="Horizontal">
                            <controls:HIcon IconName="wrench" IconSize="18" Margin="0,0,10,0"/>
                            <TextBlock Text="系统配置"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource IndustrialButton}" Margin="0,5" HorizontalAlignment="Stretch">
                        <StackPanel Orientation="Horizontal">
                            <controls:HIcon IconName="save" IconSize="18" Margin="0,0,10,0"/>
                            <TextBlock Text="数据管理"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Border>

            <!-- Right Panel - Main Content -->
            <Border Grid.Column="1" Background="{StaticResource SurfaceBrush}"
                    BorderBrush="{StaticResource BorderBrush}" BorderThickness="1"
                    CornerRadius="4">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <!-- Welcome Icon -->
                    <controls:HIcon IconName="industry" IconSize="64"
                                    IconColor="{StaticResource AccentBrush}"
                                    Margin="0,0,0,20"/>

                    <TextBlock Text="{Binding WelcomeMessage}"
                               Style="{StaticResource TitleText}"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,30"/>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Style="{StaticResource IndustrialButton}"
                                Command="{Binding StartInspectionCommand}"
                                Margin="10" Padding="20,10">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="play" IconSize="20" Margin="0,0,8,0"/>
                                <TextBlock Text="开始检测" FontSize="16"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource IndustrialButton}"
                                Margin="10" Padding="20,10">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="pause" IconSize="20" Margin="0,0,8,0"/>
                                <TextBlock Text="暂停检测" FontSize="16"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource IndustrialButton}"
                                Margin="10" Padding="20,10">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="stop" IconSize="20" Margin="0,0,8,0"/>
                                <TextBlock Text="停止检测" FontSize="16"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- Status Icons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,30,0,0">
                        <StackPanel Margin="20,0" HorizontalAlignment="Center">
                            <controls:HIcon IconName="check" Style="{StaticResource SuccessIcon}" IconSize="24"/>
                            <TextBlock Text="系统正常" Style="{StaticResource IndustrialTextBlock}"
                                       HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        </StackPanel>

                        <StackPanel Margin="20,0" HorizontalAlignment="Center">
                            <controls:HIcon IconName="camera" IconSize="24"/>
                            <TextBlock Text="摄像头就绪" Style="{StaticResource IndustrialTextBlock}"
                                       HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        </StackPanel>

                        <StackPanel Margin="20,0" HorizontalAlignment="Center">
                            <controls:HIcon IconName="microchip" IconSize="24"/>
                            <TextBlock Text="AI模型加载" Style="{StaticResource IndustrialTextBlock}"
                                       HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- Status Bar -->
        <Border Grid.Row="2" Background="{StaticResource AccentBrush}" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Status Icon and Message -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <controls:HIcon IconName="info" IconSize="14" IconColor="White" Margin="0,0,8,0"/>
                    <TextBlock Text="{Binding StatusMessage}"
                               Foreground="White"
                               FontSize="12"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Right Status Info -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <controls:HIcon IconName="gauge" IconSize="14" IconColor="White" Margin="0,0,5,0"/>
                    <TextBlock Text="CPU: 45%" Foreground="White" FontSize="12" Margin="0,0,15,0"/>

                    <controls:HIcon IconName="microchip" IconSize="14" IconColor="White" Margin="0,0,5,0"/>
                    <TextBlock Text="内存: 2.1GB" Foreground="White" FontSize="12" Margin="0,0,15,0"/>

                    <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy-MM-dd HH:mm:ss'}"
                               Foreground="White" FontSize="12"
                               xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
